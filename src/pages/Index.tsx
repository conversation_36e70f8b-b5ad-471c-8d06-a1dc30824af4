import { useState, useMemo, useCallback } from "react";
import { Users, Target, Calendar, TrendingUp, UserCheck, UserX, RotateCcw, XCircle, CheckCircle, Bug, Loader2 } from "lucide-react";
import { isWithinInterval, parseISO, startOfWeek, endOfWeek, subDays } from "date-fns";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

// Components
import { Navigation } from "@/components/layout/Navigation";
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { AppointmentsTable } from "@/components/dashboard/AppointmentsTable";
import { AppointmentBreakdown } from "@/components/dashboard/AppointmentBreakdown";
import { TimeSeriesChart } from "@/components/dashboard/TimeSeriesChart";
import { MetricCard } from "@/components/ui/metric-card";
import { DebugPanel } from "@/components/DebugPanel";
import { DiagnosticPanel } from "@/components/DiagnosticPanel";
import { ErrorResolutionTest } from "@/components/ErrorResolutionTest";
import { Http500FixSummary } from "@/components/Http500FixSummary";

// Hooks and Services
import { useAppointments } from "@/hooks/useAppointments";
import { useAuth } from "@/contexts/AuthContext";
import { Appointment } from "@/services/appointmentsService";

// Import test helpers for debugging
import "@/utils/testHelpers";

const Index = () => {
  const { profile } = useAuth();
  const today = new Date();
  const [filters, setFilters] = useState({
    dateRange: {
      start: startOfWeek(today, { weekStartsOn: 1 }),
      end: endOfWeek(today, { weekStartsOn: 1 })
    },
    selectedClosers: [] as string[],
    selectedSetters: [] as string[],
    selectedCompany: undefined as string | undefined
  });

  const [visibleMetrics, setVisibleMetrics] = useState({
    totalAppointments: true,
    totalSits: true,
    totalCloses: true,
    noShows: true,
    rescheduled: true,
    notInterested: true,
    disqualified: true,
    appointmentBreakdown: true,
    detailedTable: true,
    performanceChart: true
  });

  // Use real appointments data
  const {
    appointments,
    loading,
    error,
    metrics,
    uniqueClosers: closers,
    uniqueSetters: setters
  } = useAppointments(filters);

  // Filter appointments based on current filters
  const filteredAppointments = useMemo(() => {
    return appointments.filter(appointment => {
      // Date range filter
      const appointmentDate = parseISO(appointment.booked_for);
      const isInDateRange = isWithinInterval(appointmentDate, {
        start: filters.dateRange.start,
        end: filters.dateRange.end
      });

      // Closer filter
      const closerMatch = filters.selectedClosers.length === 0 ||
        filters.selectedClosers.includes(appointment.closer_name);

      // Setter filter
      const setterMatch = filters.selectedSetters.length === 0 ||
        filters.selectedSetters.includes(appointment.setter_name || '');

      // Company filter
      const companyMatch = !filters.selectedCompany ||
        appointment.company_id === filters.selectedCompany;

      return isInDateRange && closerMatch && setterMatch && companyMatch;
    });
  }, [appointments, filters]);

  // Calculate additional metrics for filtered appointments
  const filteredMetrics = useMemo(() => {
    const total = filteredAppointments.length;
    const sits = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Sat').length;
    const closes = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Closed').length;
    const noShows = filteredAppointments.filter(apt => apt.confirmation_disposition === 'No Show').length;
    const rescheduled = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Rescheduled').length;
    const notInterested = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Not Interested').length;
    const disqualified = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Disqualified').length;

    // Calculate percentage change vs previous period
    const currentPeriodDays = Math.ceil((filters.dateRange.end.getTime() - filters.dateRange.start.getTime()) / (1000 * 60 * 60 * 24));
    const previousStart = subDays(filters.dateRange.start, currentPeriodDays);
    const previousEnd = subDays(filters.dateRange.end, currentPeriodDays);

    const previousAppointments = appointments.filter(appointment => {
      const appointmentDate = parseISO(appointment.booked_for);
      return isWithinInterval(appointmentDate, {
        start: previousStart,
        end: previousEnd
      });
    });

    const previousTotal = previousAppointments.length;
    const percentageChange = previousTotal > 0 ? (((total - previousTotal) / previousTotal) * 100) : 0;

    return {
      total,
      sits,
      closes,
      noShows,
      rescheduled,
      notInterested,
      disqualified,
      percentageChange: percentageChange.toFixed(1),
      percentageChangeNumeric: percentageChange,
      noShowsPercentage: total > 0 ? ((noShows / total) * 100).toFixed(1) : '0.0',
      rescheduledPercentage: total > 0 ? ((rescheduled / total) * 100).toFixed(1) : '0.0',
      notInterestedPercentage: total > 0 ? ((notInterested / total) * 100).toFixed(1) : '0.0',
      disqualifiedPercentage: total > 0 ? ((disqualified / total) * 100).toFixed(1) : '0.0',
    };
  }, [filteredAppointments, filters.dateRange, appointments]);

  const toggleMetric = (metric: string) => {
    setVisibleMetrics(prev => ({
      ...prev,
      [metric]: !prev[metric]
    }));
  };

  const handleFiltersChange = useCallback((newFilters: typeof filters) => {
    setFilters(newFilters);
  }, []);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-dashboard-bg">
        <Navigation />
        <div className="container mx-auto px-6 py-6">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading appointments...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Show error state with diagnostics
  if (error) {
    return (
      <div className="min-h-screen bg-dashboard-bg">
        <Navigation />
        <div className="container mx-auto px-6 py-6">
          <div className="space-y-6">
            <div className="text-center">
              <p className="text-destructive mb-4">Error loading appointments: {error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
              >
                Retry
              </button>
            </div>
            <div className="space-y-6">
              <ErrorResolutionTest />
              <DiagnosticPanel />
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-dashboard-bg">
      <Navigation />
      <DashboardHeader
        visibleMetrics={visibleMetrics}
        onToggleMetric={toggleMetric}
      />

      <main className="container mx-auto px-6 py-6 relative">
        {/* Filters */}
        <FilterBar
          closers={closers}
          setters={setters}
          onFiltersChange={handleFiltersChange}
          loading={loading}
          profile={profile}
        />

        {/* Loading overlay for smooth transitions */}
        {loading && (
          <div className="absolute inset-0 bg-background/50 backdrop-blur-sm z-10 flex items-center justify-center">
            <div className="flex items-center gap-2 bg-card p-4 rounded-lg shadow-lg">
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
              <span className="text-sm text-muted-foreground">Updating data...</span>
            </div>
          </div>
        )}

        {/* Performance Over Time Chart */}
        {visibleMetrics.performanceChart && (
          <TimeSeriesChart appointments={filteredAppointments} dateRange={filters.dateRange} />
        )}

        {/* KPI Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {visibleMetrics.totalAppointments && (
            <MetricCard
              title="Total Appointments"
              value={filteredMetrics.total}
              subtitle={
                <span>
                  All appointments in selected period<br />
                  <span className="text-blue-600 font-semibold">
                    Change: {filteredMetrics.percentageChangeNumeric > 0 ? '+' : ''}{filteredMetrics.percentageChange}% vs previous period
                  </span>
                </span>
              }
              icon={Calendar}
              variant="info"
            />
          )}
          {visibleMetrics.totalSits && (
            <MetricCard
              title="Total Sits"
              value={filteredMetrics.sits}
              subtitle={
                <span>
                  Successfully sat appointments<br />
                  <span className="text-green-600 font-semibold">
                    Show Rate: {filteredMetrics.total > 0 ? ((filteredMetrics.sits / filteredMetrics.total) * 100).toFixed(1) : '0.0'}%
                  </span>
                </span>
              }
              icon={UserCheck}
              variant="success"
            />
          )}
          {visibleMetrics.totalCloses && (
            <MetricCard
              title="Total Closes"
              value={filteredMetrics.closes}
              subtitle={
                <span>
                  Successfully closed deals<br />
                  <span className="text-green-600 font-semibold">
                    Close Rate: {filteredMetrics.total > 0 ? ((filteredMetrics.closes / filteredMetrics.total) * 100).toFixed(1) : '0.0'}%
                  </span>
                </span>
              }
              icon={CheckCircle}
              variant="success"
            />
          )}
        </div>

        {/* Disposition Analysis Section */}
        {(visibleMetrics.noShows || visibleMetrics.rescheduled || visibleMetrics.notInterested || visibleMetrics.disqualified) && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-foreground mb-4">Disposition Analysis</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {visibleMetrics.noShows && (
                <MetricCard
                  title="No Show"
                  value={filteredMetrics.noShows}
                  subtitle={`${filteredMetrics.noShowsPercentage}% of total appointments`}
                  icon={XCircle}
                  variant="default"
                />
              )}
              {visibleMetrics.rescheduled && (
                <MetricCard
                  title="Rescheduled"
                  value={filteredMetrics.rescheduled}
                  subtitle={`${filteredMetrics.rescheduledPercentage}% of total appointments`}
                  icon={RotateCcw}
                  variant="warning"
                />
              )}
              {visibleMetrics.notInterested && (
                <MetricCard
                  title="Not Interested"
                  value={filteredMetrics.notInterested}
                  subtitle={`${filteredMetrics.notInterestedPercentage}% of total appointments`}
                  icon={UserX}
                  variant="default"
                />
              )}
              {visibleMetrics.disqualified && (
                <MetricCard
                  title="Disqualified"
                  value={filteredMetrics.disqualified}
                  subtitle={`${filteredMetrics.disqualifiedPercentage}% of total appointments`}
                  icon={Target}
                  variant="default"
                />
              )}
            </div>
          </div>
        )}

        {/* Appointment Breakdown Section */}
        {visibleMetrics.appointmentBreakdown && (
          <AppointmentBreakdown appointments={filteredAppointments} />
        )}

        {/* Detailed Appointments Table */}
        {visibleMetrics.detailedTable && (
          <AppointmentsTable appointments={filteredAppointments} />
        )}
      </main>

      {/* Debug Panel for Development */}
      <DebugPanel />
    </div>
  );
};

export default Index;
