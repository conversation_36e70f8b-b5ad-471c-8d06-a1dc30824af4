import { supabase } from '@/lib/supabase'
import { Profile } from '@/lib/supabase'

export interface CreateUserRequest {
  email: string
  password: string
  role: 'super_admin' | 'user'
  company_id?: string | null
}

export interface UpdateUserRequest {
  id: string
  role?: 'super_admin' | 'user'
  company_id?: string | null
}

export interface UserManagementResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}

/**
 * User Management Service using Edge Functions
 * This service provides a secure way to manage users by using Supabase Edge Functions
 * which have elevated permissions to bypass RLS restrictions.
 */
export class UserManagementService {
  private static readonly EDGE_FUNCTION_URL = '/functions/v1/manage-users'

  /**
   * Get the authorization header for Edge Function calls
   */
  private static async getAuthHeader(): Promise<string | null> {
    const { data: { session } } = await supabase.auth.getSession()
    return session?.access_token ? `Bearer ${session.access_token}` : null
  }

  /**
   * Make a request to the Edge Function
   */
  private static async makeRequest<T>(
    endpoint: string, 
    method: 'GET' | 'POST' = 'GET', 
    body?: any
  ): Promise<UserManagementResponse<T>> {
    try {
      const authHeader = await this.getAuthHeader()
      if (!authHeader) {
        return { success: false, error: 'Not authenticated' }
      }

      const { data, error } = await supabase.functions.invoke('manage-users', {
        body: body ? { ...body, _endpoint: endpoint } : { _endpoint: endpoint },
        headers: {
          Authorization: authHeader,
        },
      })

      if (error) {
        console.error('Edge function error:', error)
        return { success: false, error: error.message || 'Edge function call failed' }
      }

      if (data.error) {
        return { success: false, error: data.error }
      }

      return { success: true, data: data }
    } catch (error) {
      console.error('UserManagementService error:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Create a new user using Edge Function
   */
  static async createUser(userData: CreateUserRequest): Promise<UserManagementResponse<{ user: any, profile: Profile }>> {
    try {
      const authHeader = await this.getAuthHeader()
      if (!authHeader) {
        return { success: false, error: 'Not authenticated' }
      }

      const { data, error } = await supabase.functions.invoke('manage-users', {
        body: userData,
        headers: {
          Authorization: authHeader,
        },
      })

      if (error) {
        console.error('Create user error:', error)
        return { success: false, error: error.message || 'Failed to create user' }
      }

      if (data.error) {
        return { success: false, error: data.error }
      }

      return { success: true, data: data }
    } catch (error) {
      console.error('Create user error:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Update an existing user using Edge Function
   */
  static async updateUser(userData: UpdateUserRequest): Promise<UserManagementResponse<{ profile: Profile }>> {
    return this.makeRequest('update', 'POST', userData)
  }

  /**
   * Get all users using Edge Function
   */
  static async getAllUsers(): Promise<UserManagementResponse<{ users: Profile[] }>> {
    return this.makeRequest('list', 'GET')
  }

  /**
   * Fallback method: Try direct database access first, then Edge Function
   */
  static async fetchUsersWithFallback(): Promise<UserManagementResponse<Profile[]>> {
    try {
      // First, try direct database access
      const { data: profiles, error: directError } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })

      if (!directError && profiles) {
        console.log('✅ Direct database access successful')
        return { success: true, data: profiles }
      }

      console.log('⚠️ Direct access failed, trying Edge Function...', directError?.message)

      // Fallback to Edge Function
      const edgeResult = await this.getAllUsers()
      if (edgeResult.success && edgeResult.data) {
        return { success: true, data: edgeResult.data.users }
      }

      return { 
        success: false, 
        error: `Both direct access and Edge Function failed. Direct: ${directError?.message}, Edge: ${edgeResult.error}` 
      }

    } catch (error) {
      console.error('Fetch users with fallback error:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Test user creation without actually creating a user
   */
  static async testUserCreationPermissions(): Promise<UserManagementResponse<any>> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        return { success: false, error: 'Not authenticated' }
      }

      // Check current user's profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError) {
        return { success: false, error: `Cannot access own profile: ${profileError.message}` }
      }

      if (profile.role !== 'super_admin') {
        return { success: false, error: 'User is not a super admin' }
      }

      // Test write access to profiles table
      const testData = {
        id: user.id,
        email: user.email || '<EMAIL>',
        role: profile.role,
        company_id: profile.company_id
      }

      const { error: writeError } = await supabase
        .from('profiles')
        .upsert(testData, { onConflict: 'id' })

      if (writeError) {
        return { 
          success: false, 
          error: `Write access test failed: ${writeError.message}`,
          data: { 
            canReadOwnProfile: true,
            canWriteProfiles: false,
            writeError: writeError.message,
            suggestedSolution: 'Use Edge Functions for user management'
          }
        }
      }

      return { 
        success: true, 
        data: { 
          canReadOwnProfile: true,
          canWriteProfiles: true,
          userRole: profile.role,
          message: 'Direct database access should work'
        }
      }

    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
}
