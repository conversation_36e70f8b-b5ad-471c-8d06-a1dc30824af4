import { supabase, Profile } from '@/lib/supabase'
import { UserManagementService } from './userManagementService'

export interface UserFetchResult {
  success: boolean
  users: Profile[]
  count: number
  method: string
  error?: string
  metadata?: any
}

/**
 * Enhanced user service with multiple fetching strategies to handle RLS issues
 */
export class UserService {
  
  /**
   * Primary method to fetch all users with fallback strategies
   */
  static async fetchAllUsers(): Promise<UserFetchResult> {
    console.log('🔍 UserService: Starting enhanced user fetch...')
    
    // Strategy 1: Standard query (current approach)
    const standardResult = await this.fetchUsersStandard()
    if (standardResult.success && standardResult.count > 1) {
      console.log('✅ Standard query successful with multiple users')
      return standardResult
    }
    
    console.log('⚠️ Standard query returned limited results, trying alternatives...')
    
    // Strategy 2: CTE-style query with explicit field selection
    const cteResult = await this.fetchUsersWithCTE()
    if (cteResult.success && cteResult.count > standardResult.count) {
      console.log('✅ CTE-style query found more users')
      return cteResult
    }
    
    // Strategy 3: Multiple smaller queries
    const multiQueryResult = await this.fetchUsersMultiQuery()
    if (multiQueryResult.success && multiQueryResult.count > standardResult.count) {
      console.log('✅ Multi-query approach found more users')
      return multiQueryResult
    }
    
    // Strategy 4: Role-based fetching
    const roleBasedResult = await this.fetchUsersByRole()
    if (roleBasedResult.success && roleBasedResult.count > standardResult.count) {
      console.log('✅ Role-based query found more users')
      return roleBasedResult
    }

    // Strategy 5: Edge Function approach (fallback for RLS issues)
    const edgeFunctionResult = await this.fetchUsersViaEdgeFunction()
    if (edgeFunctionResult.success && edgeFunctionResult.count > 0) {
      console.log('✅ Edge Function approach found users')
      return edgeFunctionResult
    }
    
    // If all strategies return the same limited result, return the best one
    console.log('⚠️ All strategies returned similar results, using standard approach')
    return standardResult.success ? standardResult : {
      success: false,
      users: [],
      count: 0,
      method: 'all-failed',
      error: 'All fetching strategies failed'
    }
  }
  
  /**
   * Strategy 5: Use Edge Function for user management (bypasses RLS)
   */
  private static async fetchUsersViaEdgeFunction(): Promise<UserFetchResult> {
    try {
      console.log('📊 Strategy 5: Edge Function approach...')

      const result = await UserManagementService.fetchUsersWithFallback()

      if (!result.success || !result.data) {
        return {
          success: false,
          users: [],
          count: 0,
          method: 'edge-function',
          error: result.error || 'Edge function failed'
        }
      }

      return {
        success: true,
        users: result.data,
        count: result.data.length,
        method: 'edge-function'
      }
    } catch (error) {
      return {
        success: false,
        users: [],
        count: 0,
        method: 'edge-function',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Strategy 1: Standard query (current approach)
   */
  private static async fetchUsersStandard(): Promise<UserFetchResult> {
    try {
      console.log('📊 Strategy 1: Standard query...')
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })
      
      if (error) {
        console.error('Standard query error:', error)
        return {
          success: false,
          users: [],
          count: 0,
          method: 'standard',
          error: error.message
        }
      }
      
      return {
        success: true,
        users: data || [],
        count: data?.length || 0,
        method: 'standard'
      }
    } catch (error) {
      return {
        success: false,
        users: [],
        count: 0,
        method: 'standard',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
  
  /**
   * Strategy 2: CTE-style query with explicit field selection
   */
  private static async fetchUsersWithCTE(): Promise<UserFetchResult> {
    try {
      console.log('📊 Strategy 2: CTE-style query...')
      
      // Use explicit field selection and different ordering
      const { data, error, count } = await supabase
        .from('profiles')
        .select(`
          id,
          email,
          role,
          company_id,
          created_at,
          updated_at
        `, { count: 'exact' })
        .order('email', { ascending: true }) // Different ordering
      
      if (error) {
        console.error('CTE query error:', error)
        return {
          success: false,
          users: [],
          count: 0,
          method: 'cte',
          error: error.message
        }
      }
      
      return {
        success: true,
        users: data || [],
        count: data?.length || 0,
        method: 'cte',
        metadata: { totalCount: count }
      }
    } catch (error) {
      return {
        success: false,
        users: [],
        count: 0,
        method: 'cte',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
  
  /**
   * Strategy 3: Multiple smaller queries to bypass potential RLS conflicts
   */
  private static async fetchUsersMultiQuery(): Promise<UserFetchResult> {
    try {
      console.log('📊 Strategy 3: Multi-query approach...')
      
      // First, get user IDs
      const { data: userIds, error: idsError } = await supabase
        .from('profiles')
        .select('id')
      
      if (idsError || !userIds) {
        return {
          success: false,
          users: [],
          count: 0,
          method: 'multi-query',
          error: idsError?.message || 'No user IDs found'
        }
      }
      
      console.log(`Found ${userIds.length} user IDs, fetching details...`)
      
      // Then fetch each user's details
      const users: Profile[] = []
      for (const { id } of userIds) {
        const { data: user, error: userError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', id)
          .single()
        
        if (!userError && user) {
          users.push(user)
        }
      }
      
      return {
        success: true,
        users,
        count: users.length,
        method: 'multi-query',
        metadata: { totalIds: userIds.length, fetchedUsers: users.length }
      }
    } catch (error) {
      return {
        success: false,
        users: [],
        count: 0,
        method: 'multi-query',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
  
  /**
   * Strategy 4: Fetch users by role to bypass potential role-based RLS
   */
  private static async fetchUsersByRole(): Promise<UserFetchResult> {
    try {
      console.log('📊 Strategy 4: Role-based query...')
      
      // Fetch super_admin users
      const { data: superAdmins, error: adminError } = await supabase
        .from('profiles')
        .select('*')
        .eq('role', 'super_admin')
      
      // Fetch regular users
      const { data: regularUsers, error: userError } = await supabase
        .from('profiles')
        .select('*')
        .eq('role', 'user')
      
      if (adminError && userError) {
        return {
          success: false,
          users: [],
          count: 0,
          method: 'role-based',
          error: `Admin error: ${adminError.message}, User error: ${userError.message}`
        }
      }
      
      const allUsers = [
        ...(superAdmins || []),
        ...(regularUsers || [])
      ]
      
      // Sort by created_at
      allUsers.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      
      return {
        success: true,
        users: allUsers,
        count: allUsers.length,
        method: 'role-based',
        metadata: {
          superAdmins: superAdmins?.length || 0,
          regularUsers: regularUsers?.length || 0,
          adminError: adminError?.message,
          userError: userError?.message
        }
      }
    } catch (error) {
      return {
        success: false,
        users: [],
        count: 0,
        method: 'role-based',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
  
  /**
   * Test all strategies and return detailed results
   */
  static async testAllStrategies(): Promise<{
    strategies: UserFetchResult[]
    bestStrategy: string
    summary: any
  }> {
    console.log('🧪 Testing all user fetching strategies...')
    
    const strategies = [
      await this.fetchUsersStandard(),
      await this.fetchUsersWithCTE(),
      await this.fetchUsersMultiQuery(),
      await this.fetchUsersByRole()
    ]
    
    // Find the strategy that returned the most users
    const bestStrategy = strategies.reduce((best, current) => 
      current.count > best.count ? current : best
    )
    
    const summary = {
      totalStrategies: strategies.length,
      successfulStrategies: strategies.filter(s => s.success).length,
      maxUsersFound: Math.max(...strategies.map(s => s.count)),
      bestMethod: bestStrategy.method,
      allCounts: strategies.map(s => ({ method: s.method, count: s.count, success: s.success }))
    }
    
    console.log('🧪 Strategy test summary:', summary)
    
    return {
      strategies,
      bestStrategy: bestStrategy.method,
      summary
    }
  }
}
