import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from '@/components/ui/sonner'
import { supabase } from '@/lib/supabase'
import { debugUserData, testUserCreation, checkRLSAccess, testRLSPolicies, fetchUsersWithCTE, fetchUsersAlternative } from '@/utils/userDebugHelpers'

export const UserManagementTest = () => {
  const [testEmail, setTestEmail] = useState('')
  const [testPassword, setTestPassword] = useState('')
  const [testRole, setTestRole] = useState<'user' | 'super_admin'>('user')
  const [testCompanyId, setTestCompanyId] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [testResults, setTestResults] = useState<any>(null)

  const runFullTest = async () => {
    setIsLoading(true)
    setTestResults(null)

    try {
      console.log('🧪 Starting comprehensive user management test...')
      
      // Step 1: Check current state
      console.log('📊 Step 1: Checking current user data...')
      const initialDebug = await debugUserData()
      const rlsCheck = await checkRLSAccess()
      
      // Step 2: Test user creation if test data provided
      let creationResult = null
      if (testEmail && testPassword) {
        console.log('👤 Step 2: Testing user creation...')
        creationResult = await testUserCreation(
          testEmail, 
          testPassword, 
          testRole, 
          testCompanyId || undefined
        )
      }
      
      // Step 3: Check final state
      console.log('📊 Step 3: Checking final user data...')
      const finalDebug = await debugUserData()
      
      const results = {
        initialState: initialDebug,
        rlsAccess: rlsCheck,
        userCreation: creationResult,
        finalState: finalDebug,
        summary: {
          initialUserCount: initialDebug.totalUsers,
          finalUserCount: finalDebug.totalUsers,
          userCreated: creationResult?.success || false,
          rlsWorking: rlsCheck.success
        }
      }
      
      setTestResults(results)
      console.log('✅ Test completed:', results)
      
      if (creationResult?.success) {
        toast.success(`Test completed! User created successfully. Total users: ${finalDebug.totalUsers}`)
      } else {
        toast.success(`Test completed! Current users: ${finalDebug.totalUsers}`)
      }
      
    } catch (error) {
      console.error('❌ Test failed:', error)
      toast.error('Test failed. Check console for details.')
    } finally {
      setIsLoading(false)
    }
  }

  const quickDebug = async () => {
    setIsLoading(true)
    try {
      const debugInfo = await debugUserData()
      const rlsInfo = await checkRLSAccess()
      
      console.log('Quick Debug Results:', { debugInfo, rlsInfo })
      toast.success(`Found ${debugInfo.totalUsers} users. RLS: ${rlsInfo.success ? 'OK' : 'FAILED'}`)
      
      setTestResults({ debugInfo, rlsInfo })
    } catch (error) {
      console.error('Debug failed:', error)
      toast.error('Debug failed. Check console.')
    } finally {
      setIsLoading(false)
    }
  }

  const testProfileFetch = async () => {
    setIsLoading(true)
    try {
      console.log('🔍 Testing profile fetch...')

      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Profile fetch error:', error)
        toast.error(`Profile fetch failed: ${error.message}`)
      } else {
        console.log('Profiles fetched:', profiles)
        toast.success(`Successfully fetched ${profiles?.length || 0} profiles`)
        setTestResults({ profiles, count: profiles?.length || 0 })
      }
    } catch (error) {
      console.error('Test failed:', error)
      toast.error('Test failed')
    } finally {
      setIsLoading(false)
    }
  }

  const testRLSPoliciesDetailed = async () => {
    setIsLoading(true)
    try {
      console.log('🔒 Testing RLS policies in detail...')
      const results = await testRLSPolicies()
      console.log('RLS Policy Test Results:', results)
      toast.success('RLS policy test completed. Check console for details.')
      setTestResults(results)
    } catch (error) {
      console.error('RLS test failed:', error)
      toast.error('RLS test failed')
    } finally {
      setIsLoading(false)
    }
  }

  const testCTEFetch = async () => {
    setIsLoading(true)
    try {
      console.log('🔧 Testing CTE-based fetch...')
      const results = await fetchUsersWithCTE()
      console.log('CTE Fetch Results:', results)
      toast.success(`CTE fetch completed. Found ${results.users?.length || 0} users.`)
      setTestResults(results)
    } catch (error) {
      console.error('CTE test failed:', error)
      toast.error('CTE test failed')
    } finally {
      setIsLoading(false)
    }
  }

  const testAlternativeFetch = async () => {
    setIsLoading(true)
    try {
      console.log('🔄 Testing alternative fetch strategies...')
      const results = await fetchUsersAlternative()
      console.log('Alternative Fetch Results:', results)
      toast.success(`Alternative fetch completed. Best strategy: ${results.bestStrategy || 'None found'}`)
      setTestResults(results)
    } catch (error) {
      console.error('Alternative fetch test failed:', error)
      toast.error('Alternative fetch test failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>User Management Test Suite</CardTitle>
        <CardDescription>
          Test and debug user creation and display functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Quick Actions */}
        <div className="flex gap-2 flex-wrap">
          <Button onClick={quickDebug} disabled={isLoading} variant="outline">
            Quick Debug
          </Button>
          <Button onClick={testProfileFetch} disabled={isLoading} variant="outline">
            Test Profile Fetch
          </Button>
          <Button onClick={testRLSPoliciesDetailed} disabled={isLoading} variant="outline">
            Test RLS Policies
          </Button>
          <Button onClick={testCTEFetch} disabled={isLoading} variant="outline">
            Test CTE Fetch
          </Button>
          <Button onClick={testAlternativeFetch} disabled={isLoading} variant="outline">
            Test Alternative Strategies
          </Button>
        </div>

        {/* Test User Creation */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Test User Creation</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="test-email">Test Email</Label>
              <Input
                id="test-email"
                type="email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <Label htmlFor="test-password">Test Password</Label>
              <Input
                id="test-password"
                type="password"
                value={testPassword}
                onChange={(e) => setTestPassword(e.target.value)}
                placeholder="password123"
              />
            </div>
            <div>
              <Label htmlFor="test-role">Role</Label>
              <Select value={testRole} onValueChange={(value: 'user' | 'super_admin') => setTestRole(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="super_admin">Super Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="test-company">Company ID (optional)</Label>
              <Input
                id="test-company"
                value={testCompanyId}
                onChange={(e) => setTestCompanyId(e.target.value)}
                placeholder="company-123"
              />
            </div>
          </div>
          <Button onClick={runFullTest} disabled={isLoading || !testEmail || !testPassword}>
            {isLoading ? 'Running Test...' : 'Run Full Test'}
          </Button>
        </div>

        {/* Results */}
        {testResults && (
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Test Results</h3>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
              {JSON.stringify(testResults, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
