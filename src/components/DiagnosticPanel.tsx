import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Database, 
  User, 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  Bug
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { DatabaseService } from '@/services/databaseService'
import { AppointmentsService } from '@/services/appointmentsService'
import { UserService } from '@/services/userService'
import { supabase } from '@/lib/supabase'
import { validateUserManagementFix, generateValidationSummary } from '@/utils/validateUserManagementFix'

interface DiagnosticResult {
  test: string
  status: 'success' | 'error' | 'warning' | 'pending'
  message: string
  details?: any
}

export const DiagnosticPanel: React.FC = () => {
  const [results, setResults] = useState<DiagnosticResult[]>([])
  const [running, setRunning] = useState(false)
  const { user, profile, session } = useAuth()

  const addResult = (result: DiagnosticResult) => {
    setResults(prev => [...prev, result])
  }

  const runUserManagementValidation = async () => {
    setRunning(true)
    setResults([])

    try {
      addResult({
        test: 'User Management Validation',
        status: 'pending',
        message: 'Running comprehensive user management validation...'
      })

      const validationResults = await validateUserManagementFix()
      const summary = generateValidationSummary(validationResults)

      // Clear previous results and add validation results
      setResults([])

      // Add summary result
      addResult({
        test: 'Validation Summary',
        status: summary.status === 'excellent' ? 'success' : summary.status === 'good' ? 'warning' : 'error',
        message: `${summary.passed}/${summary.total} tests passed (${summary.passRate}%)`,
        details: summary
      })

      // Add individual validation results
      validationResults.forEach(result => {
        addResult({
          test: result.test,
          status: result.status === 'pass' ? 'success' : result.status === 'fail' ? 'error' : 'warning',
          message: result.message,
          details: result.details
        })
      })

    } catch (error) {
      addResult({
        test: 'Validation Error',
        status: 'error',
        message: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      })
    } finally {
      setRunning(false)
    }
  }

  const runDiagnostics = async () => {
    setRunning(true)
    setResults([])

    try {
      // Test 1: Authentication
      addResult({
        test: 'Authentication',
        status: user ? 'success' : 'error',
        message: user ? `Authenticated as ${user.email}` : 'Not authenticated',
        details: { userId: user?.id, email: user?.email }
      })

      // Test 2: Profile
      addResult({
        test: 'Profile',
        status: profile ? 'success' : 'error',
        message: profile ? `Profile loaded: ${profile.role}` : 'No profile found',
        details: { profileId: profile?.id, role: profile?.role, companyId: profile?.company_id }
      })

      // Test 3: Session
      addResult({
        test: 'Session',
        status: session ? 'success' : 'error',
        message: session ? 'Valid session' : 'No session',
        details: { 
          hasAccessToken: !!session?.access_token,
          expiresAt: session?.expires_at ? new Date(session.expires_at * 1000).toISOString() : null
        }
      })

      // Test 4: Database Connection
      try {
        const dbTest = await DatabaseService.testConnection()
        addResult({
          test: 'Database Connection',
          status: dbTest.success ? 'success' : 'error',
          message: dbTest.success ? 'Database connected' : `Database error: ${dbTest.error}`,
          details: dbTest
        })
      } catch (error) {
        addResult({
          test: 'Database Connection',
          status: 'error',
          message: `Database test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          details: error
        })
      }

      // Test 5: Appointments Query
      try {
        const appointments = await AppointmentsService.getAppointments()
        addResult({
          test: 'Appointments Query',
          status: 'success',
          message: `Found ${appointments.length} appointments`,
          details: { count: appointments.length, sample: appointments[0] }
        })
      } catch (error) {
        addResult({
          test: 'Appointments Query',
          status: 'error',
          message: `Appointments query failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          details: error
        })
      }

      // Test 6: Companies Query
      try {
        const { data: companies, error } = await supabase
          .from('companies')
          .select('*')
          .limit(5)

        if (error) {
          addResult({
            test: 'Companies Query',
            status: 'error',
            message: `Companies query failed: ${error.message}`,
            details: error
          })
        } else {
          addResult({
            test: 'Companies Query',
            status: 'success',
            message: `Found ${companies?.length || 0} companies`,
            details: { count: companies?.length, companies }
          })
        }
      } catch (error) {
        addResult({
          test: 'Companies Query',
          status: 'error',
          message: `Companies query failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          details: error
        })
      }

      // Test 7: RLS Policy Test
      if (profile) {
        try {
          const companyAccess = await DatabaseService.getUserCompanyAccess(profile.id)
          addResult({
            test: 'RLS Policy Test',
            status: 'success',
            message: `Access to ${companyAccess.companyIds.length} companies`,
            details: companyAccess
          })
        } catch (error) {
          addResult({
            test: 'RLS Policy Test',
            status: 'error',
            message: `RLS test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            details: error
          })
        }
      }

      // Test 8: User Management - Profiles Read Access
      try {
        const { data: profiles, error: profilesError, count } = await supabase
          .from('profiles')
          .select('*', { count: 'exact' })

        if (profilesError) {
          addResult({
            test: 'Profiles Read Access',
            status: 'error',
            message: `Profiles read failed: ${profilesError.message}`,
            details: profilesError
          })
        } else {
          addResult({
            test: 'Profiles Read Access',
            status: 'success',
            message: `Can read ${count} profiles`,
            details: { count, profiles: profiles?.map(p => ({ id: p.id, email: p.email, role: p.role })) }
          })
        }
      } catch (error) {
        addResult({
          test: 'Profiles Read Access',
          status: 'error',
          message: `Profiles read failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          details: error
        })
      }

      // Test 9: User Management - Profiles Write Access (Safe Test)
      if (user && profile) {
        try {
          const testData = {
            id: user.id,
            email: user.email || '<EMAIL>',
            role: profile.role,
            company_id: profile.company_id
          }

          const { error: writeError } = await supabase
            .from('profiles')
            .upsert(testData, { onConflict: 'id' })

          if (writeError) {
            addResult({
              test: 'Profiles Write Access',
              status: 'error',
              message: `Profiles write failed: ${writeError.message}`,
              details: writeError
            })
          } else {
            addResult({
              test: 'Profiles Write Access',
              status: 'success',
              message: 'Can write to profiles table',
              details: { testData }
            })
          }
        } catch (error) {
          addResult({
            test: 'Profiles Write Access',
            status: 'error',
            message: `Profiles write failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            details: error
          })
        }
      }

      // Test 10: User Service Strategies
      try {
        const userServiceResult = await UserService.fetchAllUsers()
        addResult({
          test: 'User Service',
          status: userServiceResult.success ? 'success' : 'error',
          message: userServiceResult.success
            ? `UserService found ${userServiceResult.count} users using ${userServiceResult.method} method`
            : `UserService failed: ${userServiceResult.error}`,
          details: userServiceResult
        })
      } catch (error) {
        addResult({
          test: 'User Service',
          status: 'error',
          message: `UserService failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          details: error
        })
      }

    } catch (error) {
      addResult({
        test: 'Diagnostic Runner',
        status: 'error',
        message: `Diagnostic failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      })
    } finally {
      setRunning(false)
    }
  }

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <RefreshCw className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusVariant = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return 'default'
      case 'error':
        return 'destructive'
      case 'warning':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="h-5 w-5" />
          System Diagnostics
        </CardTitle>
        <div className="flex gap-2">
          <Button onClick={runDiagnostics} disabled={running}>
            {running ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Running...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Run Diagnostics
              </>
            )}
          </Button>
          <Button onClick={runUserManagementValidation} disabled={running} variant="outline">
            <Shield className="h-4 w-4 mr-2" />
            Validate User Management Fix
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="results" className="w-full">
          <TabsList>
            <TabsTrigger value="results">Test Results</TabsTrigger>
            <TabsTrigger value="environment">Environment</TabsTrigger>
            <TabsTrigger value="raw">Raw Data</TabsTrigger>
          </TabsList>
          
          <TabsContent value="results" className="space-y-4">
            {results.length === 0 && !running && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Click "Run Diagnostics" to test the system components.
                </AlertDescription>
              </Alert>
            )}
            
            {results.map((result, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(result.status)}
                  <div>
                    <p className="font-medium">{result.test}</p>
                    <p className="text-sm text-muted-foreground">{result.message}</p>
                  </div>
                </div>
                <Badge variant={getStatusVariant(result.status)}>
                  {result.status}
                </Badge>
              </div>
            ))}
          </TabsContent>
          
          <TabsContent value="environment" className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium">Supabase URL:</p>
                <p className="text-muted-foreground">{import.meta.env.VITE_SUPABASE_URL ? 'Set' : 'Missing'}</p>
              </div>
              <div>
                <p className="font-medium">Supabase Key:</p>
                <p className="text-muted-foreground">{import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Set' : 'Missing'}</p>
              </div>
              <div>
                <p className="font-medium">Environment:</p>
                <p className="text-muted-foreground">{process.env.NODE_ENV}</p>
              </div>
              <div>
                <p className="font-medium">User Agent:</p>
                <p className="text-muted-foreground text-xs">{navigator.userAgent}</p>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="raw" className="space-y-4">
            <div className="space-y-4">
              <details className="border rounded p-3">
                <summary className="font-medium cursor-pointer">Authentication Data</summary>
                <pre className="mt-2 text-xs overflow-auto">
                  {JSON.stringify({ user, profile, session: session ? { ...session, access_token: '***' } : null }, null, 2)}
                </pre>
              </details>
              
              <details className="border rounded p-3">
                <summary className="font-medium cursor-pointer">Test Results</summary>
                <pre className="mt-2 text-xs overflow-auto">
                  {JSON.stringify(results, null, 2)}
                </pre>
              </details>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
