import { useState, useCallback, useEffect } from "react";
import { format, startOfWeek, endOfWeek } from "date-fns";
import { Calendar as CalendarIcon, Filter, RotateCcw, X, Loader2, Building2, Users, UserCheck } from "lucide-react";
import { DateRange } from "react-day-picker";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useCompanies, Company } from "@/hooks/useCompanies";
import { Profile } from "@/lib/supabase";

// Debounce utility
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

interface FilterBarProps {
  closers: string[];
  setters: string[];
  onFiltersChange: (filters: {
    dateRange: { start: Date; end: Date };
    selectedClosers: string[];
    selectedSetters: string[];
    selectedCompany?: string;
  }) => void;
  loading?: boolean;
  profile: Profile | null;
}

export function FilterBar({ closers, setters, onFiltersChange, loading = false, profile }: FilterBarProps) {
  const today = new Date();
  const [dateRange, setDateRange] = useState({
    start: startOfWeek(today, { weekStartsOn: 1 }),
    end: endOfWeek(today, { weekStartsOn: 1 })
  });
  const [selectedClosers, setSelectedClosers] = useState<string[]>([]);
  const [selectedSetters, setSelectedSetters] = useState<string[]>([]);
  const [selectedCompany, setSelectedCompany] = useState<string>("all");
  const [lastClickedDate, setLastClickedDate] = useState<Date | null>(null);
  const [isApplyingFilters, setIsApplyingFilters] = useState(false);

  // Fetch companies - always call the hook to avoid Rules of Hooks violation
  const { companies: allCompanies, loading: companiesLoading } = useCompanies();

  // Only use companies data for super admins
  const companies = profile?.role === 'super_admin' ? allCompanies : [];
  const shouldShowCompanyFilter = profile?.role === 'super_admin';

  // Debounce the filters to prevent excessive API calls
  const debouncedFilters = useDebounce({
    dateRange,
    selectedClosers,
    selectedSetters,
    selectedCompany: selectedCompany === "all" ? undefined : selectedCompany
  }, 300);

  // Use debounced filters to trigger API calls
  useEffect(() => {
    onFiltersChange(debouncedFilters);
  }, [debouncedFilters, onFiltersChange]);

  const handleDateRangeChange = useCallback((newRange: { start: Date; end: Date }) => {
    setDateRange(newRange);
    setIsApplyingFilters(true);
    // The debounced effect will handle the API call
  }, []);



  const clearDateRange = useCallback(() => {
    const defaultRange = {
      start: startOfWeek(today, { weekStartsOn: 1 }),
      end: endOfWeek(today, { weekStartsOn: 1 })
    };

    setDateRange(defaultRange);
    setLastClickedDate(null);
    setIsApplyingFilters(true);
    // The debounced effect will handle the API call
  }, [today]);

  const resetFilters = useCallback(() => {
    const defaultRange = {
      start: startOfWeek(today, { weekStartsOn: 1 }),
      end: endOfWeek(today, { weekStartsOn: 1 })
    };

    setDateRange(defaultRange);
    setSelectedClosers([]);
    setSelectedSetters([]);
    setSelectedCompany("all");
    setLastClickedDate(null);
    setIsApplyingFilters(true);
    // The debounced effect will handle the API call
  }, [today]);

  // Reset applying state when loading changes
  useEffect(() => {
    if (!loading) {
      setIsApplyingFilters(false);
    }
  }, [loading]);

  return (
    <Card className="p-6 mb-6 bg-dashboard-surface border-border shadow-sm">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-primary" />
            <span className="text-lg font-semibold text-foreground">Filters</span>
            {(loading || isApplyingFilters) && (
              <Loader2 className="h-4 w-4 animate-spin text-primary" />
            )}
          </div>

          {/* Reset Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={resetFilters}
            className="flex items-center gap-2 hover:bg-destructive/10 hover:text-destructive hover:border-destructive/20"
          >
            <RotateCcw className="h-3 w-3" />
            Reset All
          </Button>
        </div>

        {/* Filter Controls */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 items-end">{/* Date Range will be first, then company (if super admin), then closer, then setter */}

          {/* Date Range Picker */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Date Range</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full min-w-[280px] justify-start text-left font-normal",
                    "hover:bg-dashboard-surface-hover"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {format(dateRange.start, "MMM dd")} - {format(dateRange.end, "MMM dd, yyyy")}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <div className="p-3 border-b flex items-center justify-between">
                  <span className="text-sm font-medium">Select Date Range</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearDateRange}
                    className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3 mr-1" />
                    Clear
                  </Button>
                </div>
                <Calendar
                  mode="range"
                  defaultMonth={dateRange.start}
                  selected={{
                    from: dateRange.start,
                    to: dateRange.end
                  } as DateRange}
                  onSelect={(range: DateRange | undefined) => {
                    if (range?.from) {
                      if (lastClickedDate && lastClickedDate.getTime() === range.from.getTime()) {
                        // Double click - set both start and end to the same date
                        handleDateRangeChange({ start: range.from, end: range.from });
                        setLastClickedDate(null);
                      } else {
                        // Single click - always set as start date
                        setLastClickedDate(range.from);
                        if (range.to) {
                          handleDateRangeChange({ start: range.from, end: range.to });
                        } else {
                          handleDateRangeChange({ start: range.from, end: range.from });
                        }
                      }
                    }
                  }}
                  numberOfMonths={2}
                  className="pointer-events-auto"
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Company Filter - Only for Super Admins */}
          {shouldShowCompanyFilter && (
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Company</label>
              <Select
                value={selectedCompany}
                onValueChange={(value) => {
                  setSelectedCompany(value);
                  setIsApplyingFilters(true);
                }}
              >
                <SelectTrigger className="w-full min-w-[200px]">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    <SelectValue placeholder="All Companies" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Companies</SelectItem>
                  {companiesLoading ? (
                    <SelectItem value="loading" disabled>
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-3 w-3 animate-spin" />
                        Loading companies...
                      </div>
                    </SelectItem>
                  ) : (
                    companies.map((company) => (
                      <SelectItem key={company.company_id} value={company.company_id}>
                        {company.company_name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Closer Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Closer</label>
            <Select
              value={selectedClosers.length === 0 ? "all" : selectedClosers[0]}
              onValueChange={(value) => {
                if (value === "all") {
                  setSelectedClosers([]);
                } else {
                  setSelectedClosers([value]);
                }
                setIsApplyingFilters(true);
              }}
            >
              <SelectTrigger className="w-full min-w-[180px]">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <SelectValue placeholder="All Closers" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Closers</SelectItem>
                {closers.map((closer) => (
                  <SelectItem key={closer} value={closer}>
                    {closer}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Setter Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Setter</label>
            <Select
              value={selectedSetters.length === 0 ? "all" : selectedSetters[0]}
              onValueChange={(value) => {
                if (value === "all") {
                  setSelectedSetters([]);
                } else {
                  setSelectedSetters([value]);
                }
                setIsApplyingFilters(true);
              }}
            >
              <SelectTrigger className="w-full min-w-[180px]">
                <div className="flex items-center gap-2">
                  <UserCheck className="h-4 w-4" />
                  <SelectValue placeholder="All Setters" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Setters</SelectItem>
                {setters.map((setter) => (
                  <SelectItem key={setter} value={setter}>
                    {setter}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </Card>
  );
}