import React, { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { AlertCircle, CheckCircle, XCircle, User, Database, Shield } from 'lucide-react'

interface DiagnosticResult {
  test: string
  status: 'success' | 'error' | 'warning'
  message: string
  details?: any
}

export const UserManagementDiagnostic: React.FC = () => {
  const [results, setResults] = useState<DiagnosticResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const addResult = (result: DiagnosticResult) => {
    setResults(prev => [...prev, result])
  }

  const runDiagnostics = async () => {
    setIsRunning(true)
    setResults([])

    try {
      // Test 1: Check current authentication status
      addResult({ test: 'Authentication Check', status: 'success', message: 'Starting diagnostics...' })
      
      const { data: { user }, error: authError } = await supabase.auth.getUser()
      
      if (authError) {
        addResult({
          test: 'Authentication Check',
          status: 'error',
          message: `Auth error: ${authError.message}`,
          details: authError
        })
        return
      }

      if (!user) {
        addResult({
          test: 'Authentication Check',
          status: 'warning',
          message: 'No authenticated user found',
        })
        return
      }

      addResult({
        test: 'Authentication Check',
        status: 'success',
        message: `Authenticated as: ${user.email}`,
        details: { userId: user.id, email: user.email }
      })

      // Test 2: Check current user's profile
      const { data: currentProfile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError) {
        addResult({
          test: 'Current User Profile',
          status: 'error',
          message: `Profile fetch error: ${profileError.message}`,
          details: profileError
        })
      } else {
        addResult({
          test: 'Current User Profile',
          status: 'success',
          message: `Profile found: ${currentProfile.role}`,
          details: currentProfile
        })
      }

      // Test 3: Test profiles table read access
      const { data: allProfiles, error: readError, count } = await supabase
        .from('profiles')
        .select('*', { count: 'exact' })

      if (readError) {
        addResult({
          test: 'Profiles Read Access',
          status: 'error',
          message: `Read error: ${readError.message}`,
          details: readError
        })
      } else {
        addResult({
          test: 'Profiles Read Access',
          status: 'success',
          message: `Can read ${count} profiles`,
          details: { count, profiles: allProfiles?.map(p => ({ id: p.id, email: p.email, role: p.role })) }
        })
      }

      // Test 4: Test profiles table write access (safe test)
      const testProfileData = {
        id: user.id,
        email: user.email || '<EMAIL>',
        role: currentProfile?.role || 'user',
        company_id: currentProfile?.company_id || null
      }

      const { error: writeError } = await supabase
        .from('profiles')
        .upsert(testProfileData, { onConflict: 'id' })

      if (writeError) {
        addResult({
          test: 'Profiles Write Access',
          status: 'error',
          message: `Write error: ${writeError.message}`,
          details: writeError
        })
      } else {
        addResult({
          test: 'Profiles Write Access',
          status: 'success',
          message: 'Can write to profiles table',
        })
      }

      // Test 5: Test companies table access
      const { data: companies, error: companiesError } = await supabase
        .from('companies')
        .select('*')
        .limit(5)

      if (companiesError) {
        addResult({
          test: 'Companies Table Access',
          status: 'error',
          message: `Companies error: ${companiesError.message}`,
          details: companiesError
        })
      } else {
        addResult({
          test: 'Companies Table Access',
          status: 'success',
          message: `Can access companies (${companies?.length} found)`,
          details: companies
        })
      }

      // Test 6: Test user creation simulation (without actually creating)
      addResult({
        test: 'User Creation Flow Analysis',
        status: 'success',
        message: 'Analyzing user creation flow...',
        details: {
          currentUserRole: currentProfile?.role,
          canCreateUsers: currentProfile?.role === 'super_admin',
          requiredPermissions: ['profiles:insert', 'auth.users:create']
        }
      })

    } catch (error) {
      addResult({
        test: 'Diagnostic Error',
        status: 'error',
        message: `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      })
    } finally {
      setIsRunning(false)
    }
  }

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: DiagnosticResult['status']) => {
    const variants = {
      success: 'default',
      error: 'destructive',
      warning: 'secondary'
    } as const

    return (
      <Badge variant={variants[status]} className="ml-2">
        {status.toUpperCase()}
      </Badge>
    )
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          User Management Diagnostic Tool
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            onClick={runDiagnostics} 
            disabled={isRunning}
            className="flex items-center gap-2"
          >
            <Database className="h-4 w-4" />
            {isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'}
          </Button>
        </div>

        {results.length > 0 && (
          <div className="space-y-3">
            <Separator />
            <h3 className="text-lg font-semibold">Diagnostic Results</h3>
            
            {results.map((result, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(result.status)}
                    <span className="font-medium">{result.test}</span>
                    {getStatusBadge(result.status)}
                  </div>
                </div>
                
                <p className="text-sm text-muted-foreground">{result.message}</p>
                
                {result.details && (
                  <details className="text-xs">
                    <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                      View Details
                    </summary>
                    <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default UserManagementDiagnostic
