import { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { AppointmentsService, Appointment, AppointmentFilters } from '@/services/appointmentsService'
import { DatabaseService } from '@/services/databaseService'
import { toast } from '@/components/ui/sonner'

export const useAppointments = (filters?: AppointmentFilters) => {
  const { profile, user } = useAuth()
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [debugInfo, setDebugInfo] = useState<any>(null)

  // Use ref to track if we're already fetching to prevent duplicate calls
  const isFetchingRef = useRef(false)
  // Use ref to track if component is mounted to prevent state updates on unmounted components
  const isMountedRef = useRef(true)
  // Use ref to track last profile ID to prevent unnecessary refetches
  const lastProfileIdRef = useRef<string | null>(null)

  // Memoize filters to prevent unnecessary re-renders
  const memoizedFilters = useMemo(() => {
    if (!filters) return undefined

    return {
      dateRange: filters.dateRange,
      selectedClosers: filters.selectedClosers,
      selectedSetters: filters.selectedSetters,
      selectedCompany: filters.selectedCompany
    }
  }, [
    filters?.dateRange?.start?.getTime(),
    filters?.dateRange?.end?.getTime(),
    filters?.selectedClosers?.length,
    filters?.selectedClosers?.join(','),
    filters?.selectedSetters?.length,
    filters?.selectedSetters?.join(','),
    filters?.selectedCompany
  ])

  const fetchAppointments = useCallback(async () => {
    if (process.env.NODE_ENV === 'development') {
      console.log('fetchAppointments called, profile:', profile?.id, 'user:', user?.id)
    }

    if (!profile) {
      if (process.env.NODE_ENV === 'development') {
        console.log('No profile available, setting loading to false')
      }
      if (isMountedRef.current) {
        setLoading(false)
        setAppointments([])
        setError(null)
        setDebugInfo({ reason: 'no_profile', user: user?.id, profile: null })
      }
      return
    }

    // Prevent duplicate calls
    if (isFetchingRef.current) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Already fetching appointments, skipping duplicate call')
      }
      return
    }

    try {
      isFetchingRef.current = true
      if (process.env.NODE_ENV === 'development') {
        console.log('Fetching appointments for profile:', profile.id, 'role:', profile.role)
      }
      if (isMountedRef.current) {
        setLoading(true)
        setError(null)
      }

      // Test database connection first
      const connectionTest = await DatabaseService.testConnection()
      if (process.env.NODE_ENV === 'development') {
        console.log('Database connection test:', connectionTest)
      }

      if (!connectionTest.success) {
        if (isMountedRef.current) {
          setError(`Database connection failed: ${connectionTest.error}`)
          setDebugInfo({
            reason: 'connection_failed',
            profile: profile.id,
            error: connectionTest.error,
            details: connectionTest.details
          })
        }
        return
      }

      // Get user's company access
      const companyAccess = await DatabaseService.getUserCompanyAccess(profile.id)
      if (process.env.NODE_ENV === 'development') {
        console.log('User company access:', companyAccess)
      }

      if (isMountedRef.current) {
        setDebugInfo({
          reason: 'fetching',
          profile: profile.id,
          role: profile.role,
          companyAccess,
          filters: memoizedFilters
        })
      }

      const data = await AppointmentsService.getAppointments(memoizedFilters)
      if (process.env.NODE_ENV === 'development') {
        console.log('Appointments fetched:', data.length, 'appointments')
      }

      if (isMountedRef.current) {
        setAppointments(data)
        setDebugInfo({
          reason: 'success',
          profile: profile.id,
          role: profile.role,
          companyAccess,
          appointmentCount: data.length,
          filters: memoizedFilters
        })
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch appointments'
      console.error('Error fetching appointments:', err)

      if (isMountedRef.current) {
        setError(errorMessage)
        setDebugInfo({
          reason: 'error',
          profile: profile.id,
          error: errorMessage,
          originalError: err
        })

        // Only show toast for unexpected errors, not permission issues
        if (!errorMessage.includes('permission') && !errorMessage.includes('RLS')) {
          toast.error(errorMessage)
        }
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false)
      }
      isFetchingRef.current = false
    }
  }, [profile?.id, user?.id, memoizedFilters])

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('useAppointments effect triggered, profile:', profile?.id, 'lastProfileId:', lastProfileIdRef.current, 'filters:', memoizedFilters)
    }

    // Only fetch if profile ID has actually changed or this is the first load
    if (profile?.id !== lastProfileIdRef.current) {
      lastProfileIdRef.current = profile?.id || null
      fetchAppointments()
    } else if (process.env.NODE_ENV === 'development') {
      console.log('Profile ID unchanged, skipping fetch')
    }
  }, [fetchAppointments, profile?.id])

  // Cleanup effect to prevent state updates on unmounted component
  useEffect(() => {
    return () => {
      isMountedRef.current = false
    }
  }, [])

  const createAppointment = async (appointmentData: Omit<Appointment, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const newAppointment = await AppointmentsService.createAppointment(appointmentData)
      setAppointments(prev => [newAppointment, ...prev])
      toast.success('Appointment created successfully')
      return newAppointment
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create appointment'
      toast.error(errorMessage)
      throw err
    }
  }

  const updateAppointment = async (id: string, updates: Partial<Appointment>) => {
    try {
      const updatedAppointment = await AppointmentsService.updateAppointment(id, updates)
      setAppointments(prev => 
        prev.map(apt => apt.id === id ? updatedAppointment : apt)
      )
      toast.success('Appointment updated successfully')
      return updatedAppointment
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update appointment'
      toast.error(errorMessage)
      throw err
    }
  }

  const deleteAppointment = async (id: string) => {
    try {
      await AppointmentsService.deleteAppointment(id)
      setAppointments(prev => prev.filter(apt => apt.id !== id))
      toast.success('Appointment deleted successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete appointment'
      toast.error(errorMessage)
      throw err
    }
  }

  // Calculate metrics from appointments
  const metrics = useMemo(() => {
    const total = appointments.length
    const sits = appointments.filter(apt => apt.confirmation_disposition === 'Sat').length
    const closes = appointments.filter(apt => apt.confirmation_disposition === 'Closed').length
    const noShows = appointments.filter(apt => apt.confirmation_disposition === 'No Show').length
    const rescheduled = appointments.filter(apt => apt.confirmation_disposition === 'Rescheduled').length
    const notInterested = appointments.filter(apt => apt.confirmation_disposition === 'Not Interested').length
    const disqualified = appointments.filter(apt => apt.confirmation_disposition === 'Disqualified').length

    return {
      total,
      sits,
      closes,
      noShows,
      rescheduled,
      notInterested,
      disqualified,
      sitRate: total > 0 ? (sits / total) * 100 : 0,
      closeRate: sits > 0 ? (closes / sits) * 100 : 0,
      showRate: total > 0 ? ((total - noShows) / total) * 100 : 0
    }
  }, [appointments])

  // Get unique closers and setters
  const uniqueClosers = useMemo(() => {
    return [...new Set(appointments.map(apt => apt.closer_name))].sort()
  }, [appointments])

  const uniqueSetters = useMemo(() => {
    return [...new Set(appointments.map(apt => apt.setter_name).filter(Boolean))].sort()
  }, [appointments])

  return {
    appointments,
    loading,
    error,
    debugInfo,
    metrics,
    uniqueClosers,
    uniqueSetters,
    createAppointment,
    updateAppointment,
    deleteAppointment,
    refetch: fetchAppointments
  }
}
