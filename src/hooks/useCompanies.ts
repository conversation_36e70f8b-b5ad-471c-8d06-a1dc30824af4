import { useState, useEffect, useRef } from 'react'
import { supabase } from '@/lib/supabase'

export interface Company {
  id: string
  company_id: string
  company_name: string
  created_at: string
}

export const useCompanies = () => {
  const [companies, setCompanies] = useState<Company[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const isMountedRef = useRef(true)

  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        if (isMountedRef.current) {
          setLoading(true)
          setError(null)
        }

        const { data, error: fetchError } = await supabase
          .from('companies')
          .select('id, company_id, company_name, created_at')
          .order('company_name', { ascending: true })

        if (!isMountedRef.current) return

        if (fetchError) {
          if (process.env.NODE_ENV === 'development') {
            console.error('Error fetching companies:', fetchError)
          }
          setError('Failed to fetch companies')
          return
        }

        setCompanies(data || [])
      } catch (err) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Unexpected error fetching companies:', err)
        }
        if (isMountedRef.current) {
          setError('An unexpected error occurred')
        }
      } finally {
        if (isMountedRef.current) {
          setLoading(false)
        }
      }
    }

    fetchCompanies()
  }, [])

  useEffect(() => {
    return () => {
      isMountedRef.current = false
    }
  }, [])

  return {
    companies,
    loading,
    error
  }
}
