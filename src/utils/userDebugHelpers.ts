import { supabase } from '@/lib/supabase'

export interface UserDebugInfo {
  totalUsers: number
  users: any[]
  authUsers: any[]
  profilesWithoutAuth: any[]
  authWithoutProfiles: any[]
}

/**
 * Debug helper to analyze user data discrepancies
 */
export const debugUserData = async (): Promise<UserDebugInfo> => {
  console.log('🔍 Starting user data debug analysis...')
  
  try {
    // Fetch all profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false })

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError)
      throw profilesError
    }

    console.log('📊 Profiles found:', profiles?.length || 0)
    console.log('📋 Profiles data:', profiles)

    // Try to get auth users (this might not work from client side)
    let authUsers: any[] = []
    try {
      // This will only return the current user from client side
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        authUsers = [user]
      }
    } catch (error) {
      console.log('Cannot fetch all auth users from client side')
    }

    // Analyze data
    const profileIds = new Set(profiles?.map(p => p.id) || [])
    const authIds = new Set(authUsers.map(u => u.id))

    const profilesWithoutAuth = profiles?.filter(p => !authIds.has(p.id)) || []
    const authWithoutProfiles = authUsers.filter(u => !profileIds.has(u.id))

    const debugInfo: UserDebugInfo = {
      totalUsers: profiles?.length || 0,
      users: profiles || [],
      authUsers,
      profilesWithoutAuth,
      authWithoutProfiles
    }

    console.log('🔍 Debug analysis complete:', debugInfo)
    return debugInfo

  } catch (error) {
    console.error('❌ Debug analysis failed:', error)
    throw error
  }
}

/**
 * Test user creation process
 */
export const testUserCreation = async (email: string, password: string, role: 'user' | 'super_admin', companyId?: string) => {
  console.log('🧪 Testing user creation process...')
  
  try {
    // Step 1: Create auth user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: undefined,
        data: {
          role,
          company_id: companyId || null
        }
      }
    })

    if (authError) {
      console.error('❌ Auth creation failed:', authError)
      return { success: false, error: authError.message }
    }

    console.log('✅ Auth user created:', authData.user?.id)

    if (!authData.user) {
      return { success: false, error: 'No user returned from auth' }
    }

    // Step 2: Create/update profile
    const profileData = {
      id: authData.user.id,
      email,
      role,
      company_id: companyId || null
    }

    const { error: profileError } = await supabase
      .from('profiles')
      .upsert(profileData, { onConflict: 'id' })

    if (profileError) {
      console.error('❌ Profile creation failed:', profileError)
      return { success: false, error: profileError.message }
    }

    console.log('✅ Profile created/updated')

    // Step 3: Verify profile
    const { data: verifyProfile, error: verifyError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single()

    if (verifyError) {
      console.error('❌ Profile verification failed:', verifyError)
      return { success: false, error: verifyError.message }
    }

    console.log('✅ Profile verified:', verifyProfile)

    return {
      success: true,
      userId: authData.user.id,
      profile: verifyProfile
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Check for RLS policy issues with detailed analysis
 */
export const checkRLSAccess = async () => {
  console.log('🔒 Checking RLS access...')

  try {
    // Test 1: Basic read access with count
    console.log('📊 Test 1: Basic read access with count...')
    const { data, error, count } = await supabase
      .from('profiles')
      .select('*', { count: 'exact' })

    if (error) {
      console.error('❌ RLS access denied:', error)
      return { success: false, error: error.message }
    }

    console.log('✅ RLS access granted, count:', count, 'data length:', data?.length)

    // Test 2: Try different query approaches
    console.log('📊 Test 2: Testing different query approaches...')

    // Simple select without count
    const { data: simpleData, error: simpleError } = await supabase
      .from('profiles')
      .select('id, email, role, company_id, created_at')

    console.log('Simple select result:', {
      success: !simpleError,
      count: simpleData?.length,
      error: simpleError?.message
    })

    // Select with specific ordering
    const { data: orderedData, error: orderedError } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false })

    console.log('Ordered select result:', {
      success: !orderedError,
      count: orderedData?.length,
      error: orderedError?.message
    })

    // Test 3: Check current user context
    console.log('📊 Test 3: Checking current user context...')
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (user) {
      console.log('Current user:', {
        id: user.id,
        email: user.email,
        role: user.role,
        aud: user.aud
      })

      // Try to fetch current user's profile specifically
      const { data: currentProfile, error: currentProfileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      console.log('Current user profile:', {
        success: !currentProfileError,
        profile: currentProfile,
        error: currentProfileError?.message
      })
    }

    return {
      success: true,
      count,
      dataLength: data?.length || 0,
      hasDiscrepancy: count !== (data?.length || 0),
      tests: {
        basicSelect: { success: !error, count: data?.length },
        simpleSelect: { success: !simpleError, count: simpleData?.length },
        orderedSelect: { success: !orderedError, count: orderedData?.length }
      },
      currentUser: user ? {
        id: user.id,
        email: user.email,
        hasProfile: !!data?.find(p => p.id === user.id)
      } : null
    }

  } catch (error) {
    console.error('❌ RLS check failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Test RLS policies with different user contexts and query patterns
 */
export const testRLSPolicies = async () => {
  console.log('🔍 Testing RLS policies with different approaches...')

  try {
    const results: any = {
      timestamp: new Date().toISOString(),
      tests: {}
    }

    // Get current user context
    const { data: { user } } = await supabase.auth.getUser()
    results.currentUser = user ? { id: user.id, email: user.email } : null

    // Test 1: Standard query (what we're currently using)
    console.log('🧪 Test 1: Standard profiles query...')
    const { data: standardData, error: standardError } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false })

    results.tests.standard = {
      success: !standardError,
      count: standardData?.length || 0,
      error: standardError?.message,
      data: standardData?.map(p => ({ id: p.id, email: p.email, role: p.role }))
    }

    // Test 2: Query with explicit role filter
    console.log('🧪 Test 2: Query with role filter...')
    const { data: roleData, error: roleError } = await supabase
      .from('profiles')
      .select('*')
      .in('role', ['user', 'super_admin'])
      .order('created_at', { ascending: false })

    results.tests.withRoleFilter = {
      success: !roleError,
      count: roleData?.length || 0,
      error: roleError?.message,
      data: roleData?.map(p => ({ id: p.id, email: p.email, role: p.role }))
    }

    // Test 3: Query specific user IDs if we know them
    if (standardData && standardData.length > 0) {
      console.log('🧪 Test 3: Query specific user by ID...')
      const firstUserId = standardData[0].id
      const { data: specificData, error: specificError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', firstUserId)
        .single()

      results.tests.specificUser = {
        success: !specificError,
        userId: firstUserId,
        error: specificError?.message,
        found: !!specificData
      }
    }

    // Test 4: Try to bypass RLS with a function call (if we had one)
    console.log('🧪 Test 4: Testing raw count...')
    const { count: rawCount, error: countError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })

    results.tests.rawCount = {
      success: !countError,
      count: rawCount,
      error: countError?.message
    }

    // Test 5: Check if we can see our own profile
    if (user) {
      console.log('🧪 Test 5: Check own profile access...')
      const { data: ownProfile, error: ownError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      results.tests.ownProfile = {
        success: !ownError,
        error: ownError?.message,
        found: !!ownProfile,
        profile: ownProfile ? { id: ownProfile.id, email: ownProfile.email, role: ownProfile.role } : null
      }
    }

    console.log('🔍 RLS Policy Test Results:', results)
    return results

  } catch (error) {
    console.error('❌ RLS policy test failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Fetch users using CTE-based approach to bypass potential RLS conflicts
 */
export const fetchUsersWithCTE = async () => {
  console.log('🔧 Attempting CTE-based user fetch...')

  try {
    // Since Supabase client doesn't support raw SQL CTEs directly,
    // we'll use a more sophisticated query approach

    // Approach 1: Use RPC function if available
    // This would require a database function to be created

    // Approach 2: Multi-step query to gather all user data
    console.log('📊 Step 1: Get user IDs from auth.users (if accessible)...')

    // We can't access auth.users directly from client, so let's try a different approach
    // Get all profiles with detailed error handling
    const { data: profiles, error: profilesError, count } = await supabase
      .from('profiles')
      .select(`
        id,
        email,
        role,
        company_id,
        created_at,
        updated_at
      `, { count: 'exact' })
      .order('created_at', { ascending: false })

    if (profilesError) {
      console.error('❌ CTE approach failed:', profilesError)
      return { success: false, error: profilesError.message }
    }

    console.log('✅ CTE-style query successful:', {
      count,
      dataLength: profiles?.length,
      profiles: profiles?.map(p => ({ id: p.id, email: p.email, role: p.role }))
    })

    // Approach 3: Try to get additional user data by joining with companies
    if (profiles && profiles.length > 0) {
      console.log('📊 Step 2: Enriching user data with company information...')

      const enrichedUsers = await Promise.all(
        profiles.map(async (profile) => {
          if (profile.company_id) {
            const { data: company } = await supabase
              .from('companies')
              .select('company_name')
              .eq('company_id', profile.company_id)
              .single()

            return {
              ...profile,
              company_name: company?.company_name || 'Unknown Company'
            }
          }
          return {
            ...profile,
            company_name: 'No Company'
          }
        })
      )

      return {
        success: true,
        method: 'CTE-style',
        count,
        dataLength: profiles.length,
        users: enrichedUsers
      }
    }

    return {
      success: true,
      method: 'CTE-style',
      count,
      dataLength: profiles?.length || 0,
      users: profiles || []
    }

  } catch (error) {
    console.error('❌ CTE fetch failed:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Alternative user fetching strategy that tries multiple approaches
 */
export const fetchUsersAlternative = async () => {
  console.log('🔄 Trying alternative user fetching strategies...')

  const strategies = [
    {
      name: 'Standard Query',
      fn: async () => {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .order('created_at', { ascending: false })
        return { data, error, method: 'standard' }
      }
    },
    {
      name: 'No Ordering',
      fn: async () => {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
        return { data, error, method: 'no-order' }
      }
    },
    {
      name: 'Specific Fields Only',
      fn: async () => {
        const { data, error } = await supabase
          .from('profiles')
          .select('id, email, role, company_id, created_at')
          .order('created_at', { ascending: false })
        return { data, error, method: 'specific-fields' }
      }
    },
    {
      name: 'Limit and Offset',
      fn: async () => {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .range(0, 10)
          .order('created_at', { ascending: false })
        return { data, error, method: 'limit-offset' }
      }
    }
  ]

  const results = []

  for (const strategy of strategies) {
    try {
      console.log(`🧪 Trying strategy: ${strategy.name}`)
      const result = await strategy.fn()
      results.push({
        strategy: strategy.name,
        success: !result.error,
        count: result.data?.length || 0,
        error: result.error?.message,
        method: result.method
      })

      if (!result.error && result.data && result.data.length > 1) {
        console.log(`✅ Strategy "${strategy.name}" found multiple users!`)
        return {
          success: true,
          bestStrategy: strategy.name,
          users: result.data,
          allResults: results
        }
      }
    } catch (error) {
      results.push({
        strategy: strategy.name,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  return {
    success: false,
    message: 'All strategies returned limited results',
    allResults: results
  }
}

/**
 * Create a database function to bypass RLS (requires database admin access)
 */
export const createRLSBypassFunction = async () => {
  console.log('🔧 Attempting to create RLS bypass function...')

  try {
    // This would create a database function that runs with elevated privileges
    const functionSQL = `
      CREATE OR REPLACE FUNCTION get_all_profiles_admin()
      RETURNS TABLE (
        id uuid,
        email text,
        role text,
        company_id text,
        created_at timestamptz,
        updated_at timestamptz
      )
      SECURITY DEFINER
      SET search_path = public
      LANGUAGE sql
      AS $$
        SELECT id, email, role, company_id, created_at, updated_at
        FROM profiles
        ORDER BY created_at DESC;
      $$;
    `

    // Note: This would require admin privileges to execute
    console.log('Function SQL:', functionSQL)
    console.log('⚠️ This function requires database admin privileges to create')

    return {
      success: false,
      message: 'Function creation requires admin privileges',
      sql: functionSQL
    }

  } catch (error) {
    console.error('❌ Function creation failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Test if we can call an RLS bypass function (if it exists)
 */
export const testRLSBypassFunction = async () => {
  console.log('🔧 Testing RLS bypass function...')

  try {
    // Try to call the function if it exists
    const { data, error } = await supabase.rpc('get_all_profiles_admin')

    if (error) {
      console.log('RLS bypass function not available or failed:', error.message)
      return {
        success: false,
        error: error.message,
        available: false
      }
    }

    console.log('✅ RLS bypass function successful:', data?.length, 'users')
    return {
      success: true,
      users: data,
      count: data?.length || 0,
      available: true
    }

  } catch (error) {
    console.error('❌ RLS bypass function test failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      available: false
    }
  }
}
