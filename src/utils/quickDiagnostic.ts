import { supabase } from '@/lib/supabase'

/**
 * Quick diagnostic function to check the current state of the system
 */
export const runQuickDiagnostic = async () => {
  console.log('🔍 Running Quick Diagnostic...')
  
  try {
    // Check authentication status
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    console.log('Auth Status:', { user: user ? { id: user.id, email: user.email } : null, error: authError })

    // Check profiles table access
    const { data: profiles, error: profilesError, count } = await supabase
      .from('profiles')
      .select('*', { count: 'exact' })

    console.log('Profiles Query:', { 
      count, 
      error: profilesError, 
      profiles: profiles?.map(p => ({ id: p.id, email: p.email, role: p.role, company_id: p.company_id }))
    })

    // Check companies table access
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('*')

    console.log('Companies Query:', { 
      count: companies?.length, 
      error: companiesError, 
      companies: companies?.map(c => ({ id: c.company_id, name: c.company_name }))
    })

    // Test write access (safe - just update current user's profile)
    if (user) {
      const { error: writeError } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          email: user.email || '<EMAIL>',
          role: 'user',
          company_id: null
        }, { onConflict: 'id' })

      console.log('Write Test:', { error: writeError })
    }

    return {
      auth: { user, error: authError },
      profiles: { data: profiles, error: profilesError, count },
      companies: { data: companies, error: companiesError },
      summary: {
        authenticated: !!user,
        canReadProfiles: !profilesError,
        canReadCompanies: !companiesError,
        profileCount: count || 0,
        companyCount: companies?.length || 0
      }
    }

  } catch (error) {
    console.error('Quick diagnostic failed:', error)
    return { error }
  }
}

/**
 * Test user creation flow without actually creating a user
 */
export const testUserCreationFlow = async () => {
  console.log('🧪 Testing User Creation Flow...')
  
  try {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      console.log('❌ No authenticated user - cannot test user creation')
      return { error: 'Not authenticated' }
    }

    // Get current user's profile to check permissions
    const { data: currentProfile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    console.log('Current User Profile:', { profile: currentProfile, error: profileError })

    if (currentProfile?.role !== 'super_admin') {
      console.log('❌ Current user is not super_admin - cannot create users')
      return { error: 'Insufficient permissions' }
    }

    // Test if we can read all profiles (required for user management)
    const { data: allProfiles, error: readError } = await supabase
      .from('profiles')
      .select('*')

    console.log('All Profiles Read Test:', { 
      count: allProfiles?.length, 
      error: readError,
      canManageUsers: !readError && allProfiles
    })

    // Test companies access (needed for user creation)
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('*')

    console.log('Companies Access Test:', { 
      count: companies?.length, 
      error: companiesError 
    })

    return {
      currentUser: { profile: currentProfile, error: profileError },
      profilesAccess: { data: allProfiles, error: readError },
      companiesAccess: { data: companies, error: companiesError },
      canCreateUsers: currentProfile?.role === 'super_admin' && !readError && !companiesError
    }

  } catch (error) {
    console.error('User creation flow test failed:', error)
    return { error }
  }
}

/**
 * Analyze RLS policies by testing different operations
 */
export const analyzeRLSPolicies = async () => {
  console.log('🔒 Analyzing RLS Policies...')
  
  try {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return { error: 'Not authenticated' }
    }

    const tests = []

    // Test 1: Read own profile
    const { data: ownProfile, error: ownError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    tests.push({
      test: 'Read Own Profile',
      success: !ownError,
      error: ownError?.message,
      data: ownProfile
    })

    // Test 2: Read all profiles
    const { data: allProfiles, error: allError } = await supabase
      .from('profiles')
      .select('*')

    tests.push({
      test: 'Read All Profiles',
      success: !allError,
      error: allError?.message,
      count: allProfiles?.length
    })

    // Test 3: Update own profile
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', user.id)

    tests.push({
      test: 'Update Own Profile',
      success: !updateError,
      error: updateError?.message
    })

    // Test 4: Insert/Upsert own profile
    const { error: upsertError } = await supabase
      .from('profiles')
      .upsert({
        id: user.id,
        email: user.email || '<EMAIL>',
        role: ownProfile?.role || 'user',
        company_id: ownProfile?.company_id || null
      }, { onConflict: 'id' })

    tests.push({
      test: 'Upsert Own Profile',
      success: !upsertError,
      error: upsertError?.message
    })

    console.log('RLS Policy Analysis Results:', tests)
    return { tests, currentUser: ownProfile }

  } catch (error) {
    console.error('RLS analysis failed:', error)
    return { error }
  }
}
