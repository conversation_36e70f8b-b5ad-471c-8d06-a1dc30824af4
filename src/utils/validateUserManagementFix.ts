import { supabase } from '@/lib/supabase'
import { UserManagementService } from '@/services/userManagementService'
import { UserService } from '@/services/userService'

export interface ValidationResult {
  test: string
  status: 'pass' | 'fail' | 'warning'
  message: string
  details?: any
}

/**
 * Comprehensive validation of user management fixes
 */
export const validateUserManagementFix = async (): Promise<ValidationResult[]> => {
  const results: ValidationResult[] = []

  try {
    // Test 1: Authentication Check
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      results.push({
        test: 'Authentication',
        status: 'fail',
        message: 'User not authenticated',
        details: authError
      })
      return results
    }

    results.push({
      test: 'Authentication',
      status: 'pass',
      message: `Authenticated as ${user.email}`,
      details: { userId: user.id }
    })

    // Test 2: Current User Profile Access
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError) {
      results.push({
        test: 'Profile Access',
        status: 'fail',
        message: `Cannot access own profile: ${profileError.message}`,
        details: profileError
      })
      return results
    }

    results.push({
      test: 'Profile Access',
      status: 'pass',
      message: `Profile loaded: ${profile.role}`,
      details: profile
    })

    // Test 3: Super Admin Check
    if (profile.role !== 'super_admin') {
      results.push({
        test: 'Super Admin Role',
        status: 'warning',
        message: 'User is not a super admin - limited testing possible',
        details: { currentRole: profile.role }
      })
    } else {
      results.push({
        test: 'Super Admin Role',
        status: 'pass',
        message: 'User has super admin privileges',
        details: { role: profile.role }
      })
    }

    // Test 4: Profiles Read Access
    const { data: allProfiles, error: readError, count } = await supabase
      .from('profiles')
      .select('*', { count: 'exact' })

    if (readError) {
      results.push({
        test: 'Profiles Read Access',
        status: 'fail',
        message: `Cannot read profiles: ${readError.message}`,
        details: readError
      })
    } else {
      results.push({
        test: 'Profiles Read Access',
        status: 'pass',
        message: `Can read ${count} profiles`,
        details: { count, sampleProfiles: allProfiles?.slice(0, 3) }
      })
    }

    // Test 5: Profiles Write Access (Safe Test)
    const testData = {
      id: user.id,
      email: user.email || '<EMAIL>',
      role: profile.role,
      company_id: profile.company_id
    }

    const { error: writeError } = await supabase
      .from('profiles')
      .upsert(testData, { onConflict: 'id' })

    if (writeError) {
      results.push({
        test: 'Profiles Write Access',
        status: 'fail',
        message: `Cannot write to profiles: ${writeError.message}`,
        details: writeError
      })
    } else {
      results.push({
        test: 'Profiles Write Access',
        status: 'pass',
        message: 'Can write to profiles table',
        details: { testData }
      })
    }

    // Test 6: UserService Functionality
    try {
      const userServiceResult = await UserService.fetchAllUsers()
      
      if (userServiceResult.success) {
        results.push({
          test: 'UserService',
          status: 'pass',
          message: `UserService found ${userServiceResult.count} users using ${userServiceResult.method}`,
          details: userServiceResult
        })
      } else {
        results.push({
          test: 'UserService',
          status: 'fail',
          message: `UserService failed: ${userServiceResult.error}`,
          details: userServiceResult
        })
      }
    } catch (error) {
      results.push({
        test: 'UserService',
        status: 'fail',
        message: `UserService error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      })
    }

    // Test 7: UserManagementService Permissions Test
    try {
      const permissionsTest = await UserManagementService.testUserCreationPermissions()
      
      if (permissionsTest.success) {
        results.push({
          test: 'UserManagementService Permissions',
          status: 'pass',
          message: 'User creation permissions validated',
          details: permissionsTest.data
        })
      } else {
        results.push({
          test: 'UserManagementService Permissions',
          status: 'warning',
          message: `Permissions test: ${permissionsTest.error}`,
          details: permissionsTest
        })
      }
    } catch (error) {
      results.push({
        test: 'UserManagementService Permissions',
        status: 'fail',
        message: `Permissions test error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      })
    }

    // Test 8: Companies Table Access
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('*')
      .limit(5)

    if (companiesError) {
      results.push({
        test: 'Companies Access',
        status: 'fail',
        message: `Cannot access companies: ${companiesError.message}`,
        details: companiesError
      })
    } else {
      results.push({
        test: 'Companies Access',
        status: 'pass',
        message: `Can access companies (${companies?.length} found)`,
        details: { count: companies?.length, companies }
      })
    }

    // Test 9: Edge Function Availability (if applicable)
    if (profile.role === 'super_admin') {
      try {
        const edgeFunctionTest = await UserManagementService.fetchUsersWithFallback()
        
        if (edgeFunctionTest.success) {
          results.push({
            test: 'Edge Function Fallback',
            status: 'pass',
            message: `Edge function fallback working (${edgeFunctionTest.data?.length} users)`,
            details: { userCount: edgeFunctionTest.data?.length }
          })
        } else {
          results.push({
            test: 'Edge Function Fallback',
            status: 'warning',
            message: `Edge function fallback: ${edgeFunctionTest.error}`,
            details: edgeFunctionTest
          })
        }
      } catch (error) {
        results.push({
          test: 'Edge Function Fallback',
          status: 'warning',
          message: `Edge function test error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          details: error
        })
      }
    }

  } catch (error) {
    results.push({
      test: 'Validation Error',
      status: 'fail',
      message: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: error
    })
  }

  return results
}

/**
 * Generate a summary report of validation results
 */
export const generateValidationSummary = (results: ValidationResult[]) => {
  const passed = results.filter(r => r.status === 'pass').length
  const failed = results.filter(r => r.status === 'fail').length
  const warnings = results.filter(r => r.status === 'warning').length
  const total = results.length

  const summary = {
    total,
    passed,
    failed,
    warnings,
    passRate: Math.round((passed / total) * 100),
    status: failed === 0 ? (warnings === 0 ? 'excellent' : 'good') : 'needs-attention'
  }

  console.log('🔍 User Management Validation Summary:')
  console.log(`✅ Passed: ${passed}/${total} (${summary.passRate}%)`)
  console.log(`❌ Failed: ${failed}/${total}`)
  console.log(`⚠️  Warnings: ${warnings}/${total}`)
  console.log(`📊 Overall Status: ${summary.status}`)

  return summary
}
