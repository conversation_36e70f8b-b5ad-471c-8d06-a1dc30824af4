/**
 * Test utility to verify that React key props are working correctly
 * This helps ensure that list items have unique keys to prevent React warnings
 */

import { Appointment } from '@/services/appointmentsService';

/**
 * Test function to verify that appointments have valid unique keys
 * @param appointments Array of appointments to test
 * @returns Object with test results
 */
export function testAppointmentKeys(appointments: Appointment[]) {
  const results = {
    totalAppointments: appointments.length,
    hasContactId: 0,
    hasId: 0,
    uniqueContactIds: new Set<string>(),
    uniqueIds: new Set<string>(),
    duplicateContactIds: [] as string[],
    duplicateIds: [] as string[],
    missingBothIds: 0
  };

  appointments.forEach((appointment, index) => {
    // Check contact_id
    if (appointment.contact_id) {
      results.hasContactId++;
      if (results.uniqueContactIds.has(appointment.contact_id)) {
        results.duplicateContactIds.push(appointment.contact_id);
      } else {
        results.uniqueContactIds.add(appointment.contact_id);
      }
    }

    // Check id
    if (appointment.id) {
      results.hasId++;
      if (results.uniqueIds.has(appointment.id)) {
        results.duplicateIds.push(appointment.id);
      } else {
        results.uniqueIds.add(appointment.id);
      }
    }

    // Check if both are missing
    if (!appointment.contact_id && !appointment.id) {
      results.missingBothIds++;
    }
  });

  return results;
}

/**
 * Generate a unique key for an appointment following the same logic as the component
 * @param appointment The appointment object
 * @param index The index in the array (fallback)
 * @returns A unique key string
 */
export function generateAppointmentKey(appointment: Appointment, index: number): string {
  return appointment.contact_id || appointment.id || `appointment-${index}`;
}

/**
 * Test that all generated keys are unique
 * @param appointments Array of appointments
 * @returns Test result with any duplicate keys found
 */
export function testKeyUniqueness(appointments: Appointment[]) {
  const keys = appointments.map((appointment, index) => 
    generateAppointmentKey(appointment, index)
  );
  
  const uniqueKeys = new Set(keys);
  const duplicates = keys.filter((key, index) => keys.indexOf(key) !== index);
  
  return {
    totalKeys: keys.length,
    uniqueKeys: uniqueKeys.size,
    hasDuplicates: duplicates.length > 0,
    duplicates: [...new Set(duplicates)]
  };
}

// Export for use in development/debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).testAppointmentKeys = testAppointmentKeys;
  (window as any).testKeyUniqueness = testKeyUniqueness;
  (window as any).generateAppointmentKey = generateAppointmentKey;
}
